@echo off
echo Building Leonard Wealth Nullifier mod...

cd LeonardWealthNullifier\Source

echo Compiling C# source code...
csc /target:library /out:..\Assemblies\LeonardWealthNullifier.dll /reference:System.dll /reference:System.Core.dll LeonardWealthNullifier.cs

if %ERRORLEVEL% EQU 0 (
    echo Build successful! DLL created at LeonardWealthNullifier\Assemblies\LeonardWealthNullifier.dll
) else (
    echo Build failed with error code %ERRORLEVEL%
)

cd ..\..
pause
