{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\augment-projects\\leo\\LeonardWealthNullifier\\Source\\LeonardWealthNullifier.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\augment-projects\\leo\\LeonardWealthNullifier\\Source\\LeonardWealthNullifier.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\leo\\LeonardWealthNullifier\\Source\\LeonardWealthNullifier.csproj", "projectName": "LeonardWealthNullifier", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\leo\\LeonardWealthNullifier\\Source\\LeonardWealthNullifier.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\leo\\LeonardWealthNullifier\\Source\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net40"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net40": {"targetAlias": "net40", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net40": {"targetAlias": "net40", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}