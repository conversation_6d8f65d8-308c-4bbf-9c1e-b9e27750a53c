using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Verse;
using RimWorld;
using HarmonyLib;

namespace LeonardWealthNullifier
{
    [StaticConstructorOnStartup]
    public static class LeonardWealthNullifierMod
    {
        static LeonardWealthNullifierMod()
        {
            var harmony = new Harmony("ai.leonard.wealthnullifier");
            harmony.PatchAll();
            Log.Message("[Leonard Wealth Nullifier] Mod loaded and Harmony patches applied.");
        }
    }

    public class GameComponent_LeonardWealthNullifier : GameComponent
    {
        private int lastCheckTick = 0;
        private const int CHECK_INTERVAL = 250; // Check every 250 ticks (about 4 seconds)

        public GameComponent_LeonardWealthNullifier(Game game) : base()
        {
            Log.Message("[Leonard Wealth Nullifier] GameComponent initialized");
        }

        public override void GameComponentTick()
        {
            // Only check periodically to avoid performance issues
            if (Find.TickManager.TicksGame - lastCheckTick >= CHECK_INTERVAL)
            {
                ProcessLeonardPawns();
                lastCheckTick = Find.TickManager.TicksGame;
            }
        }

        private void ProcessLeonardPawns()
        {
            try
            {
                // Get all pawns from all maps
                var allPawns = PawnsFinder.AllMaps;
                bool foundLeonard = false;

                foreach (var pawn in allPawns)
                {
                    if (IsLeonardPriemschi(pawn))
                    {
                        foundLeonard = true;
                        // Force wealth recalculation for maps containing Leonard
                        if (pawn.Map != null && pawn.Map.IsPlayerHome)
                        {
                            Log.Message($"[Leonard Wealth Nullifier] Found Leonard Priemschi: {pawn.Name.ToStringFull}, Market Value: {pawn.MarketValue}");
                        }
                    }
                }

                if (!foundLeonard)
                {
                    Log.Message("[Leonard Wealth Nullifier] No Leonard Priemschi found in any maps");
                }
            }
            catch (Exception ex)
            {
                Log.Error($"[Leonard Wealth Nullifier] Error processing Leonard pawns: {ex}");
            }
        }

        public static bool IsLeonardPriemschi(Pawn pawn)
        {
            if (pawn?.Name == null)
                return false;

            if (pawn.Name is NameTriple nameTriple)
            {
                return string.Equals(nameTriple.First, "Leonard", StringComparison.OrdinalIgnoreCase) &&
                       string.Equals(nameTriple.Last, "Priemschi", StringComparison.OrdinalIgnoreCase);
            }

            return false;
        }

        public override void ExposeData()
        {
            // No data to save/load for this component
        }
    }

    [HarmonyPatch(typeof(StatWorker_MarketValue), "GetValueUnfinalized")]
    public static class Patch_StatWorker_MarketValue_GetValueUnfinalized
    {
        public static void Postfix(StatRequest req, ref float __result)
        {
            try
            {
                if (req.HasThing && req.Thing is Pawn pawn)
                {
                    if (GameComponent_LeonardWealthNullifier.IsLeonardPriemschi(pawn))
                    {
                        __result = 0f;
                        Log.Message($"[Leonard Wealth Nullifier] Set market value to 0 for Leonard Priemschi");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error($"[Leonard Wealth Nullifier] Error in MarketValue patch: {ex}");
            }
        }
    }

    [HarmonyPatch(typeof(WealthWatcher), "ForceRecount")]
    public static class Patch_WealthWatcher_ForceRecount
    {
        private static FieldInfo mapField = typeof(WealthWatcher).GetField("map", BindingFlags.NonPublic | BindingFlags.Instance);
        private static FieldInfo wealthPawnsField = typeof(WealthWatcher).GetField("wealthPawns", BindingFlags.NonPublic | BindingFlags.Instance);

        public static void Postfix(WealthWatcher __instance)
        {
            try
            {
                // Log when wealth is being recalculated
                Log.Message("[Leonard Wealth Nullifier] Wealth recalculation triggered");

                // Manually subtract Leonard's wealth if he was counted
                var map = (Map)mapField.GetValue(__instance);
                if (map != null)
                {
                    foreach (Pawn pawn in map.mapPawns.PawnsInFaction(Faction.OfPlayer))
                    {
                        if (!pawn.IsQuestLodger() && GameComponent_LeonardWealthNullifier.IsLeonardPriemschi(pawn))
                        {
                            float leonardValue = pawn.MarketValue;
                            if (pawn.IsSlave)
                            {
                                leonardValue *= 0.75f;
                            }

                            // Subtract Leonard's value from the total pawn wealth
                            float currentWealthPawns = (float)wealthPawnsField.GetValue(__instance);
                            float newWealthPawns = currentWealthPawns - leonardValue;
                            wealthPawnsField.SetValue(__instance, newWealthPawns);
                            Log.Message($"[Leonard Wealth Nullifier] Subtracted Leonard's wealth: {leonardValue}, new pawn wealth: {newWealthPawns}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error($"[Leonard Wealth Nullifier] Error in WealthWatcher patch: {ex}");
            }
        }
    }
}
