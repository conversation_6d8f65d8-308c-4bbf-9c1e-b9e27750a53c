{"version": 3, "targets": {".NETFramework,Version=v4.0": {"Microsoft.NETFramework.ReferenceAssemblies/1.0.3": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies.net40": "1.0.3"}}, "Microsoft.NETFramework.ReferenceAssemblies.net40/1.0.3": {"type": "package", "build": {"build/Microsoft.NETFramework.ReferenceAssemblies.net40.targets": {}}}}}, "libraries": {"Microsoft.NETFramework.ReferenceAssemblies/1.0.3": {"sha512": "vUc9Npcs14QsyOD01tnv/m8sQUnGTGOw1BCmKcv77LBJY7OxhJ+zJF7UD/sCL3lYNFuqmQEVlkfS4Quif6FyYg==", "type": "package", "path": "microsoft.netframework.referenceassemblies/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "microsoft.netframework.referenceassemblies.1.0.3.nupkg.sha512", "microsoft.netframework.referenceassemblies.nuspec"]}, "Microsoft.NETFramework.ReferenceAssemblies.net40/1.0.3": {"sha512": "3ctXnCpHdoYJNH9ATfXKwckkkdHvHc1Xls12QB9kf1tjh3b+VzxtiwpkZN4GxOawakfH6CJAkqhlDKSiz6Ujbg==", "type": "package", "path": "microsoft.netframework.referenceassemblies.net40/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "build/.NETFramework/v4.0/Accessibility.dll", "build/.NETFramework/v4.0/Accessibility.xml", "build/.NETFramework/v4.0/CustomMarshalers.dll", "build/.NETFramework/v4.0/CustomMarshalers.xml", "build/.NETFramework/v4.0/ISymWrapper.dll", "build/.NETFramework/v4.0/ISymWrapper.xml", "build/.NETFramework/v4.0/Microsoft.Build.Conversion.v4.0.dll", "build/.NETFramework/v4.0/Microsoft.Build.Conversion.v4.0.xml", "build/.NETFramework/v4.0/Microsoft.Build.Engine.dll", "build/.NETFramework/v4.0/Microsoft.Build.Engine.xml", "build/.NETFramework/v4.0/Microsoft.Build.Framework.dll", "build/.NETFramework/v4.0/Microsoft.Build.Framework.xml", "build/.NETFramework/v4.0/Microsoft.Build.Tasks.v4.0.dll", "build/.NETFramework/v4.0/Microsoft.Build.Tasks.v4.0.xml", "build/.NETFramework/v4.0/Microsoft.Build.Utilities.v4.0.dll", "build/.NETFramework/v4.0/Microsoft.Build.Utilities.v4.0.xml", "build/.NETFramework/v4.0/Microsoft.Build.dll", "build/.NETFramework/v4.0/Microsoft.Build.xml", "build/.NETFramework/v4.0/Microsoft.CSharp.dll", "build/.NETFramework/v4.0/Microsoft.CSharp.xml", "build/.NETFramework/v4.0/Microsoft.JScript.dll", "build/.NETFramework/v4.0/Microsoft.JScript.xml", "build/.NETFramework/v4.0/Microsoft.VisualBasic.Compatibility.Data.dll", "build/.NETFramework/v4.0/Microsoft.VisualBasic.Compatibility.Data.xml", "build/.NETFramework/v4.0/Microsoft.VisualBasic.Compatibility.dll", "build/.NETFramework/v4.0/Microsoft.VisualBasic.Compatibility.xml", "build/.NETFramework/v4.0/Microsoft.VisualBasic.dll", "build/.NETFramework/v4.0/Microsoft.VisualBasic.xml", "build/.NETFramework/v4.0/Microsoft.VisualC.STLCLR.dll", "build/.NETFramework/v4.0/Microsoft.VisualC.STLCLR.xml", "build/.NETFramework/v4.0/Microsoft.VisualC.dll", "build/.NETFramework/v4.0/Microsoft.VisualC.xml", "build/.NETFramework/v4.0/PermissionSets/FullTrust.xml", "build/.NETFramework/v4.0/PermissionSets/Internet.xml", "build/.NETFramework/v4.0/PermissionSets/LocalIntranet.xml", "build/.NETFramework/v4.0/PresentationBuildTasks.dll", "build/.NETFramework/v4.0/PresentationBuildTasks.xml", "build/.NETFramework/v4.0/PresentationCore.dll", "build/.NETFramework/v4.0/PresentationCore.xml", "build/.NETFramework/v4.0/PresentationFramework.Aero.dll", "build/.NETFramework/v4.0/PresentationFramework.Aero.xml", "build/.NETFramework/v4.0/PresentationFramework.Classic.dll", "build/.NETFramework/v4.0/PresentationFramework.Classic.xml", "build/.NETFramework/v4.0/PresentationFramework.Luna.dll", "build/.NETFramework/v4.0/PresentationFramework.Luna.xml", "build/.NETFramework/v4.0/PresentationFramework.Royale.dll", "build/.NETFramework/v4.0/PresentationFramework.Royale.xml", "build/.NETFramework/v4.0/PresentationFramework.dll", "build/.NETFramework/v4.0/PresentationFramework.xml", "build/.NETFramework/v4.0/Profile/Client/Accessibility.dll", "build/.NETFramework/v4.0/Profile/Client/Accessibility.xml", "build/.NETFramework/v4.0/Profile/Client/CustomMarshalers.dll", "build/.NETFramework/v4.0/Profile/Client/CustomMarshalers.xml", "build/.NETFramework/v4.0/Profile/Client/Microsoft.CSharp.dll", "build/.NETFramework/v4.0/Profile/Client/Microsoft.CSharp.xml", "build/.NETFramework/v4.0/Profile/Client/Microsoft.JScript.dll", "build/.NETFramework/v4.0/Profile/Client/Microsoft.JScript.xml", "build/.NETFramework/v4.0/Profile/Client/Microsoft.VisualBasic.Compatibility.Data.dll", "build/.NETFramework/v4.0/Profile/Client/Microsoft.VisualBasic.Compatibility.Data.xml", "build/.NETFramework/v4.0/Profile/Client/Microsoft.VisualBasic.Compatibility.dll", "build/.NETFramework/v4.0/Profile/Client/Microsoft.VisualBasic.Compatibility.xml", "build/.NETFramework/v4.0/Profile/Client/Microsoft.VisualBasic.dll", "build/.NETFramework/v4.0/Profile/Client/Microsoft.VisualBasic.xml", "build/.NETFramework/v4.0/Profile/Client/Microsoft.VisualC.dll", "build/.NETFramework/v4.0/Profile/Client/Microsoft.VisualC.xml", "build/.NETFramework/v4.0/Profile/Client/PermissionSets/FullTrust.xml", "build/.NETFramework/v4.0/Profile/Client/PermissionSets/Internet.xml", "build/.NETFramework/v4.0/Profile/Client/PermissionSets/LocalIntranet.xml", "build/.NETFramework/v4.0/Profile/Client/PresentationCore.dll", "build/.NETFramework/v4.0/Profile/Client/PresentationCore.xml", "build/.NETFramework/v4.0/Profile/Client/PresentationFramework.Aero.dll", "build/.NETFramework/v4.0/Profile/Client/PresentationFramework.Aero.xml", "build/.NETFramework/v4.0/Profile/Client/PresentationFramework.Classic.dll", "build/.NETFramework/v4.0/Profile/Client/PresentationFramework.Classic.xml", "build/.NETFramework/v4.0/Profile/Client/PresentationFramework.Luna.dll", "build/.NETFramework/v4.0/Profile/Client/PresentationFramework.Luna.xml", "build/.NETFramework/v4.0/Profile/Client/PresentationFramework.Royale.dll", "build/.NETFramework/v4.0/Profile/Client/PresentationFramework.Royale.xml", "build/.NETFramework/v4.0/Profile/Client/PresentationFramework.dll", "build/.NETFramework/v4.0/Profile/Client/PresentationFramework.xml", "build/.NETFramework/v4.0/Profile/Client/ReachFramework.dll", "build/.NETFramework/v4.0/Profile/Client/ReachFramework.xml", "build/.NETFramework/v4.0/Profile/Client/RedistList/FrameworkList.xml", "build/.NETFramework/v4.0/Profile/Client/System.Activities.Core.Presentation.dll", "build/.NETFramework/v4.0/Profile/Client/System.Activities.Core.Presentation.xml", "build/.NETFramework/v4.0/Profile/Client/System.Activities.DurableInstancing.dll", "build/.NETFramework/v4.0/Profile/Client/System.Activities.DurableInstancing.xml", "build/.NETFramework/v4.0/Profile/Client/System.Activities.Presentation.dll", "build/.NETFramework/v4.0/Profile/Client/System.Activities.Presentation.xml", "build/.NETFramework/v4.0/Profile/Client/System.Activities.dll", "build/.NETFramework/v4.0/Profile/Client/System.Activities.xml", "build/.NETFramework/v4.0/Profile/Client/System.AddIn.Contract.xml", "build/.NETFramework/v4.0/Profile/Client/System.AddIn.xml", "build/.NETFramework/v4.0/Profile/Client/System.Addin.Contract.dll", "build/.NETFramework/v4.0/Profile/Client/System.Addin.dll", "build/.NETFramework/v4.0/Profile/Client/System.ComponentModel.Composition.dll", "build/.NETFramework/v4.0/Profile/Client/System.ComponentModel.Composition.xml", "build/.NETFramework/v4.0/Profile/Client/System.ComponentModel.DataAnnotations.dll", "build/.NETFramework/v4.0/Profile/Client/System.ComponentModel.DataAnnotations.xml", "build/.NETFramework/v4.0/Profile/Client/System.Configuration.Install.dll", "build/.NETFramework/v4.0/Profile/Client/System.Configuration.Install.xml", "build/.NETFramework/v4.0/Profile/Client/System.Configuration.dll", "build/.NETFramework/v4.0/Profile/Client/System.Configuration.xml", "build/.NETFramework/v4.0/Profile/Client/System.Core.dll", "build/.NETFramework/v4.0/Profile/Client/System.Core.xml", "build/.NETFramework/v4.0/Profile/Client/System.Data.DataSetExtensions.dll", "build/.NETFramework/v4.0/Profile/Client/System.Data.DataSetExtensions.xml", "build/.NETFramework/v4.0/Profile/Client/System.Data.Entity.dll", "build/.NETFramework/v4.0/Profile/Client/System.Data.Entity.xml", "build/.NETFramework/v4.0/Profile/Client/System.Data.Linq.dll", "build/.NETFramework/v4.0/Profile/Client/System.Data.Linq.xml", "build/.NETFramework/v4.0/Profile/Client/System.Data.Services.Client.dll", "build/.NETFramework/v4.0/Profile/Client/System.Data.Services.Client.xml", "build/.NETFramework/v4.0/Profile/Client/System.Data.SqlXml.dll", "build/.NETFramework/v4.0/Profile/Client/System.Data.SqlXml.xml", "build/.NETFramework/v4.0/Profile/Client/System.Data.dll", "build/.NETFramework/v4.0/Profile/Client/System.Data.xml", "build/.NETFramework/v4.0/Profile/Client/System.Deployment.dll", "build/.NETFramework/v4.0/Profile/Client/System.Deployment.xml", "build/.NETFramework/v4.0/Profile/Client/System.Device.dll", "build/.NETFramework/v4.0/Profile/Client/System.Device.xml", "build/.NETFramework/v4.0/Profile/Client/System.DirectoryServices.AccountManagement.dll", "build/.NETFramework/v4.0/Profile/Client/System.DirectoryServices.AccountManagement.xml", "build/.NETFramework/v4.0/Profile/Client/System.DirectoryServices.Protocols.dll", "build/.NETFramework/v4.0/Profile/Client/System.DirectoryServices.Protocols.xml", "build/.NETFramework/v4.0/Profile/Client/System.DirectoryServices.dll", "build/.NETFramework/v4.0/Profile/Client/System.DirectoryServices.xml", "build/.NETFramework/v4.0/Profile/Client/System.Drawing.dll", "build/.NETFramework/v4.0/Profile/Client/System.Drawing.xml", "build/.NETFramework/v4.0/Profile/Client/System.EnterpriseServices.Thunk.dll", "build/.NETFramework/v4.0/Profile/Client/System.EnterpriseServices.Wrapper.dll", "build/.NETFramework/v4.0/Profile/Client/System.EnterpriseServices.dll", "build/.NETFramework/v4.0/Profile/Client/System.EnterpriseServices.xml", "build/.NETFramework/v4.0/Profile/Client/System.IO.Log.dll", "build/.NETFramework/v4.0/Profile/Client/System.IO.Log.xml", "build/.NETFramework/v4.0/Profile/Client/System.IdentityModel.Selectors.dll", "build/.NETFramework/v4.0/Profile/Client/System.IdentityModel.Selectors.xml", "build/.NETFramework/v4.0/Profile/Client/System.IdentityModel.dll", "build/.NETFramework/v4.0/Profile/Client/System.IdentityModel.xml", "build/.NETFramework/v4.0/Profile/Client/System.Management.Instrumentation.dll", "build/.NETFramework/v4.0/Profile/Client/System.Management.Instrumentation.xml", "build/.NETFramework/v4.0/Profile/Client/System.Management.dll", "build/.NETFramework/v4.0/Profile/Client/System.Management.xml", "build/.NETFramework/v4.0/Profile/Client/System.Messaging.dll", "build/.NETFramework/v4.0/Profile/Client/System.Messaging.xml", "build/.NETFramework/v4.0/Profile/Client/System.Net.dll", "build/.NETFramework/v4.0/Profile/Client/System.Net.xml", "build/.NETFramework/v4.0/Profile/Client/System.Numerics.dll", "build/.NETFramework/v4.0/Profile/Client/System.Numerics.xml", "build/.NETFramework/v4.0/Profile/Client/System.Printing.dll", "build/.NETFramework/v4.0/Profile/Client/System.Printing.xml", "build/.NETFramework/v4.0/Profile/Client/System.Runtime.DurableInstancing.dll", "build/.NETFramework/v4.0/Profile/Client/System.Runtime.DurableInstancing.xml", "build/.NETFramework/v4.0/Profile/Client/System.Runtime.Remoting.dll", "build/.NETFramework/v4.0/Profile/Client/System.Runtime.Remoting.xml", "build/.NETFramework/v4.0/Profile/Client/System.Runtime.Serialization.Formatters.Soap.dll", "build/.NETFramework/v4.0/Profile/Client/System.Runtime.Serialization.Formatters.Soap.xml", "build/.NETFramework/v4.0/Profile/Client/System.Runtime.Serialization.dll", "build/.NETFramework/v4.0/Profile/Client/System.Runtime.Serialization.xml", "build/.NETFramework/v4.0/Profile/Client/System.Security.dll", "build/.NETFramework/v4.0/Profile/Client/System.Security.xml", "build/.NETFramework/v4.0/Profile/Client/System.ServiceModel.Activities.dll", "build/.NETFramework/v4.0/Profile/Client/System.ServiceModel.Activities.xml", "build/.NETFramework/v4.0/Profile/Client/System.ServiceModel.Channels.dll", "build/.NETFramework/v4.0/Profile/Client/System.ServiceModel.Channels.xml", "build/.NETFramework/v4.0/Profile/Client/System.ServiceModel.Discovery.dll", "build/.NETFramework/v4.0/Profile/Client/System.ServiceModel.Discovery.xml", "build/.NETFramework/v4.0/Profile/Client/System.ServiceModel.Routing.dll", "build/.NETFramework/v4.0/Profile/Client/System.ServiceModel.Routing.xml", "build/.NETFramework/v4.0/Profile/Client/System.ServiceModel.dll", "build/.NETFramework/v4.0/Profile/Client/System.ServiceModel.xml", "build/.NETFramework/v4.0/Profile/Client/System.ServiceProcess.dll", "build/.NETFramework/v4.0/Profile/Client/System.ServiceProcess.xml", "build/.NETFramework/v4.0/Profile/Client/System.Speech.dll", "build/.NETFramework/v4.0/Profile/Client/System.Speech.xml", "build/.NETFramework/v4.0/Profile/Client/System.Transactions.dll", "build/.NETFramework/v4.0/Profile/Client/System.Transactions.xml", "build/.NETFramework/v4.0/Profile/Client/System.Web.ApplicationServices.dll", "build/.NETFramework/v4.0/Profile/Client/System.Web.ApplicationServices.xml", "build/.NETFramework/v4.0/Profile/Client/System.Web.Services.dll", "build/.NETFramework/v4.0/Profile/Client/System.Web.Services.xml", "build/.NETFramework/v4.0/Profile/Client/System.Windows.Forms.DataVisualization.dll", "build/.NETFramework/v4.0/Profile/Client/System.Windows.Forms.DataVisualization.xml", "build/.NETFramework/v4.0/Profile/Client/System.Windows.Forms.dll", "build/.NETFramework/v4.0/Profile/Client/System.Windows.Forms.xml", "build/.NETFramework/v4.0/Profile/Client/System.Windows.Input.Manipulations.dll", "build/.NETFramework/v4.0/Profile/Client/System.Windows.Input.Manipulations.xml", "build/.NETFramework/v4.0/Profile/Client/System.Windows.Presentation.dll", "build/.NETFramework/v4.0/Profile/Client/System.Windows.Presentation.xml", "build/.NETFramework/v4.0/Profile/Client/System.Xaml.dll", "build/.NETFramework/v4.0/Profile/Client/System.Xaml.xml", "build/.NETFramework/v4.0/Profile/Client/System.Xml.Linq.dll", "build/.NETFramework/v4.0/Profile/Client/System.Xml.Linq.xml", "build/.NETFramework/v4.0/Profile/Client/System.Xml.dll", "build/.NETFramework/v4.0/Profile/Client/System.Xml.xml", "build/.NETFramework/v4.0/Profile/Client/System.dll", "build/.NETFramework/v4.0/Profile/Client/System.xml", "build/.NETFramework/v4.0/Profile/Client/UIAutomationClient.dll", "build/.NETFramework/v4.0/Profile/Client/UIAutomationClient.xml", "build/.NETFramework/v4.0/Profile/Client/UIAutomationClientsideProviders.dll", "build/.NETFramework/v4.0/Profile/Client/UIAutomationClientsideProviders.xml", "build/.NETFramework/v4.0/Profile/Client/UIAutomationProvider.dll", "build/.NETFramework/v4.0/Profile/Client/UIAutomationProvider.xml", "build/.NETFramework/v4.0/Profile/Client/UIAutomationTypes.dll", "build/.NETFramework/v4.0/Profile/Client/UIAutomationTypes.xml", "build/.NETFramework/v4.0/Profile/Client/WindowsBase.dll", "build/.NETFramework/v4.0/Profile/Client/WindowsBase.xml", "build/.NETFramework/v4.0/Profile/Client/WindowsFormsIntegration.dll", "build/.NETFramework/v4.0/Profile/Client/WindowsFormsIntegration.xml", "build/.NETFramework/v4.0/Profile/Client/mscorlib.dll", "build/.NETFramework/v4.0/Profile/Client/mscorlib.xml", "build/.NETFramework/v4.0/Profile/Client/sysglobl.dll", "build/.NETFramework/v4.0/Profile/Client/sysglobl.xml", "build/.NETFramework/v4.0/ReachFramework.dll", "build/.NETFramework/v4.0/ReachFramework.xml", "build/.NETFramework/v4.0/RedistList/FrameworkList.xml", "build/.NETFramework/v4.0/System.Activities.Core.Presentation.dll", "build/.NETFramework/v4.0/System.Activities.Core.Presentation.xml", "build/.NETFramework/v4.0/System.Activities.DurableInstancing.dll", "build/.NETFramework/v4.0/System.Activities.DurableInstancing.xml", "build/.NETFramework/v4.0/System.Activities.Presentation.dll", "build/.NETFramework/v4.0/System.Activities.Presentation.xml", "build/.NETFramework/v4.0/System.Activities.dll", "build/.NETFramework/v4.0/System.Activities.xml", "build/.NETFramework/v4.0/System.AddIn.Contract.dll", "build/.NETFramework/v4.0/System.AddIn.Contract.xml", "build/.NETFramework/v4.0/System.AddIn.dll", "build/.NETFramework/v4.0/System.AddIn.xml", "build/.NETFramework/v4.0/System.ComponentModel.Composition.dll", "build/.NETFramework/v4.0/System.ComponentModel.Composition.xml", "build/.NETFramework/v4.0/System.ComponentModel.DataAnnotations.dll", "build/.NETFramework/v4.0/System.ComponentModel.DataAnnotations.xml", "build/.NETFramework/v4.0/System.Configuration.Install.dll", "build/.NETFramework/v4.0/System.Configuration.Install.xml", "build/.NETFramework/v4.0/System.Configuration.dll", "build/.NETFramework/v4.0/System.Configuration.xml", "build/.NETFramework/v4.0/System.Core.dll", "build/.NETFramework/v4.0/System.Core.xml", "build/.NETFramework/v4.0/System.Data.DataSetExtensions.dll", "build/.NETFramework/v4.0/System.Data.DataSetExtensions.xml", "build/.NETFramework/v4.0/System.Data.Entity.Design.dll", "build/.NETFramework/v4.0/System.Data.Entity.Design.xml", "build/.NETFramework/v4.0/System.Data.Entity.dll", "build/.NETFramework/v4.0/System.Data.Entity.xml", "build/.NETFramework/v4.0/System.Data.Linq.dll", "build/.NETFramework/v4.0/System.Data.Linq.xml", "build/.NETFramework/v4.0/System.Data.OracleClient.dll", "build/.NETFramework/v4.0/System.Data.OracleClient.xml", "build/.NETFramework/v4.0/System.Data.Services.Client.dll", "build/.NETFramework/v4.0/System.Data.Services.Client.xml", "build/.NETFramework/v4.0/System.Data.Services.Design.dll", "build/.NETFramework/v4.0/System.Data.Services.Design.xml", "build/.NETFramework/v4.0/System.Data.Services.dll", "build/.NETFramework/v4.0/System.Data.Services.xml", "build/.NETFramework/v4.0/System.Data.SqlXml.dll", "build/.NETFramework/v4.0/System.Data.SqlXml.xml", "build/.NETFramework/v4.0/System.Data.dll", "build/.NETFramework/v4.0/System.Data.xml", "build/.NETFramework/v4.0/System.Deployment.dll", "build/.NETFramework/v4.0/System.Deployment.xml", "build/.NETFramework/v4.0/System.Design.dll", "build/.NETFramework/v4.0/System.Design.xml", "build/.NETFramework/v4.0/System.Device.dll", "build/.NETFramework/v4.0/System.Device.xml", "build/.NETFramework/v4.0/System.DirectoryServices.AccountManagement.dll", "build/.NETFramework/v4.0/System.DirectoryServices.AccountManagement.xml", "build/.NETFramework/v4.0/System.DirectoryServices.Protocols.dll", "build/.NETFramework/v4.0/System.DirectoryServices.Protocols.xml", "build/.NETFramework/v4.0/System.DirectoryServices.dll", "build/.NETFramework/v4.0/System.DirectoryServices.xml", "build/.NETFramework/v4.0/System.Drawing.Design.dll", "build/.NETFramework/v4.0/System.Drawing.Design.xml", "build/.NETFramework/v4.0/System.Drawing.dll", "build/.NETFramework/v4.0/System.Drawing.xml", "build/.NETFramework/v4.0/System.EnterpriseServices.Thunk.dll", "build/.NETFramework/v4.0/System.EnterpriseServices.Wrapper.dll", "build/.NETFramework/v4.0/System.EnterpriseServices.dll", "build/.NETFramework/v4.0/System.EnterpriseServices.xml", "build/.NETFramework/v4.0/System.IO.Log.dll", "build/.NETFramework/v4.0/System.IO.Log.xml", "build/.NETFramework/v4.0/System.IdentityModel.Selectors.dll", "build/.NETFramework/v4.0/System.IdentityModel.Selectors.xml", "build/.NETFramework/v4.0/System.IdentityModel.dll", "build/.NETFramework/v4.0/System.IdentityModel.xml", "build/.NETFramework/v4.0/System.Management.Instrumentation.dll", "build/.NETFramework/v4.0/System.Management.Instrumentation.xml", "build/.NETFramework/v4.0/System.Management.dll", "build/.NETFramework/v4.0/System.Management.xml", "build/.NETFramework/v4.0/System.Messaging.dll", "build/.NETFramework/v4.0/System.Messaging.xml", "build/.NETFramework/v4.0/System.Net.dll", "build/.NETFramework/v4.0/System.Net.xml", "build/.NETFramework/v4.0/System.Numerics.dll", "build/.NETFramework/v4.0/System.Numerics.xml", "build/.NETFramework/v4.0/System.Printing.dll", "build/.NETFramework/v4.0/System.Printing.xml", "build/.NETFramework/v4.0/System.Runtime.Caching.dll", "build/.NETFramework/v4.0/System.Runtime.Caching.xml", "build/.NETFramework/v4.0/System.Runtime.DurableInstancing.dll", "build/.NETFramework/v4.0/System.Runtime.DurableInstancing.xml", "build/.NETFramework/v4.0/System.Runtime.Remoting.dll", "build/.NETFramework/v4.0/System.Runtime.Remoting.xml", "build/.NETFramework/v4.0/System.Runtime.Serialization.Formatters.Soap.dll", "build/.NETFramework/v4.0/System.Runtime.Serialization.Formatters.Soap.xml", "build/.NETFramework/v4.0/System.Runtime.Serialization.dll", "build/.NETFramework/v4.0/System.Runtime.Serialization.xml", "build/.NETFramework/v4.0/System.Security.dll", "build/.NETFramework/v4.0/System.Security.xml", "build/.NETFramework/v4.0/System.ServiceModel.Activation.dll", "build/.NETFramework/v4.0/System.ServiceModel.Activation.xml", "build/.NETFramework/v4.0/System.ServiceModel.Activities.dll", "build/.NETFramework/v4.0/System.ServiceModel.Activities.xml", "build/.NETFramework/v4.0/System.ServiceModel.Channels.dll", "build/.NETFramework/v4.0/System.ServiceModel.Channels.xml", "build/.NETFramework/v4.0/System.ServiceModel.Discovery.dll", "build/.NETFramework/v4.0/System.ServiceModel.Discovery.xml", "build/.NETFramework/v4.0/System.ServiceModel.Routing.dll", "build/.NETFramework/v4.0/System.ServiceModel.Routing.xml", "build/.NETFramework/v4.0/System.ServiceModel.Web.dll", "build/.NETFramework/v4.0/System.ServiceModel.Web.xml", "build/.NETFramework/v4.0/System.ServiceModel.dll", "build/.NETFramework/v4.0/System.ServiceModel.xml", "build/.NETFramework/v4.0/System.ServiceProcess.dll", "build/.NETFramework/v4.0/System.ServiceProcess.xml", "build/.NETFramework/v4.0/System.Speech.dll", "build/.NETFramework/v4.0/System.Speech.xml", "build/.NETFramework/v4.0/System.Transactions.dll", "build/.NETFramework/v4.0/System.Transactions.xml", "build/.NETFramework/v4.0/System.Web.Abstractions.dll", "build/.NETFramework/v4.0/System.Web.ApplicationServices.dll", "build/.NETFramework/v4.0/System.Web.ApplicationServices.xml", "build/.NETFramework/v4.0/System.Web.DataVisualization.Design.dll", "build/.NETFramework/v4.0/System.Web.DataVisualization.dll", "build/.NETFramework/v4.0/System.Web.DataVisualization.xml", "build/.NETFramework/v4.0/System.Web.DynamicData.Design.dll", "build/.NETFramework/v4.0/System.Web.DynamicData.Design.xml", "build/.NETFramework/v4.0/System.Web.DynamicData.dll", "build/.NETFramework/v4.0/System.Web.DynamicData.xml", "build/.NETFramework/v4.0/System.Web.Entity.Design.dll", "build/.NETFramework/v4.0/System.Web.Entity.Design.xml", "build/.NETFramework/v4.0/System.Web.Entity.dll", "build/.NETFramework/v4.0/System.Web.Entity.xml", "build/.NETFramework/v4.0/System.Web.Extensions.Design.dll", "build/.NETFramework/v4.0/System.Web.Extensions.Design.xml", "build/.NETFramework/v4.0/System.Web.Extensions.dll", "build/.NETFramework/v4.0/System.Web.Extensions.xml", "build/.NETFramework/v4.0/System.Web.Mobile.dll", "build/.NETFramework/v4.0/System.Web.Mobile.xml", "build/.NETFramework/v4.0/System.Web.RegularExpressions.dll", "build/.NETFramework/v4.0/System.Web.RegularExpressions.xml", "build/.NETFramework/v4.0/System.Web.Routing.dll", "build/.NETFramework/v4.0/System.Web.Services.dll", "build/.NETFramework/v4.0/System.Web.Services.xml", "build/.NETFramework/v4.0/System.Web.dll", "build/.NETFramework/v4.0/System.Web.xml", "build/.NETFramework/v4.0/System.Windows.Forms.DataVisualization.Design.dll", "build/.NETFramework/v4.0/System.Windows.Forms.DataVisualization.dll", "build/.NETFramework/v4.0/System.Windows.Forms.DataVisualization.xml", "build/.NETFramework/v4.0/System.Windows.Forms.dll", "build/.NETFramework/v4.0/System.Windows.Forms.xml", "build/.NETFramework/v4.0/System.Windows.Input.Manipulations.dll", "build/.NETFramework/v4.0/System.Windows.Input.Manipulations.xml", "build/.NETFramework/v4.0/System.Windows.Presentation.dll", "build/.NETFramework/v4.0/System.Windows.Presentation.xml", "build/.NETFramework/v4.0/System.Workflow.Activities.dll", "build/.NETFramework/v4.0/System.Workflow.Activities.xml", "build/.NETFramework/v4.0/System.Workflow.ComponentModel.dll", "build/.NETFramework/v4.0/System.Workflow.ComponentModel.xml", "build/.NETFramework/v4.0/System.Workflow.Runtime.dll", "build/.NETFramework/v4.0/System.Workflow.Runtime.xml", "build/.NETFramework/v4.0/System.WorkflowServices.dll", "build/.NETFramework/v4.0/System.WorkflowServices.xml", "build/.NETFramework/v4.0/System.Xaml.dll", "build/.NETFramework/v4.0/System.Xaml.xml", "build/.NETFramework/v4.0/System.Xml.Linq.dll", "build/.NETFramework/v4.0/System.Xml.Linq.xml", "build/.NETFramework/v4.0/System.Xml.dll", "build/.NETFramework/v4.0/System.Xml.xml", "build/.NETFramework/v4.0/System.dll", "build/.NETFramework/v4.0/System.xml", "build/.NETFramework/v4.0/UIAutomationClient.dll", "build/.NETFramework/v4.0/UIAutomationClient.xml", "build/.NETFramework/v4.0/UIAutomationClientsideProviders.dll", "build/.NETFramework/v4.0/UIAutomationClientsideProviders.xml", "build/.NETFramework/v4.0/UIAutomationProvider.dll", "build/.NETFramework/v4.0/UIAutomationProvider.xml", "build/.NETFramework/v4.0/UIAutomationTypes.dll", "build/.NETFramework/v4.0/UIAutomationTypes.xml", "build/.NETFramework/v4.0/WindowsBase.dll", "build/.NETFramework/v4.0/WindowsBase.xml", "build/.NETFramework/v4.0/WindowsFormsIntegration.dll", "build/.NETFramework/v4.0/WindowsFormsIntegration.xml", "build/.NETFramework/v4.0/XamlBuildTask.dll", "build/.NETFramework/v4.0/XamlBuildTask.xml", "build/.NETFramework/v4.0/mscorlib.dll", "build/.NETFramework/v4.0/mscorlib.xml", "build/.NETFramework/v4.0/sysglobl.dll", "build/.NETFramework/v4.0/sysglobl.xml", "build/Microsoft.NETFramework.ReferenceAssemblies.net40.targets", "microsoft.netframework.referenceassemblies.net40.1.0.3.nupkg.sha512", "microsoft.netframework.referenceassemblies.net40.nuspec"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.0": ["Microsoft.NETFramework.ReferenceAssemblies >= 1.0.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\leo\\LeonardWealthNullifier\\Source\\LeonardWealthNullifier.csproj", "projectName": "LeonardWealthNullifier", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\leo\\LeonardWealthNullifier\\Source\\LeonardWealthNullifier.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\leo\\LeonardWealthNullifier\\Source\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net40"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net40": {"targetAlias": "net40", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net40": {"targetAlias": "net40", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}